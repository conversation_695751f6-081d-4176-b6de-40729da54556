'use client';

import { useState, useTransition } from 'react';
import { Zap } from 'lucide-react';

import { cn } from '@/lib/utils';
import { logger } from '@/lib/logger';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AutomationStatusToggleProps {
  automationId: string;
  defaultChecked?: boolean;
  disabled?: boolean;
}

export function AutomationStatusToggle({
  automationId,
  defaultChecked = false,
  disabled = false,
}: AutomationStatusToggleProps) {
  const [isActive, setIsActive] = useState(defaultChecked);
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  const handleToggle = () => {
    if (disabled || isPending) return;

    const newState = !isActive;
    setError(null);

    startTransition(async () => {
      try {
        const response = await fetch('/api/automation/toggle', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            automationId,
            isActive: newState,
          }),
        });

        const result = await response.json();

        if (result.error) {
          setError(result.error);
        } else {
          setIsActive(newState);
          // Refresh the page to show updated data
          window.location.reload();
        }
      } catch (error) {
        setError('Failed to update automation status');
        logger.error('Toggle error:', error);
      }
    });
  };

  return (
    <div className="flex items-center">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              type="button"
              onClick={handleToggle}
              className={cn(
                'flex items-center justify-center w-10 h-10 rounded-full transition-colors',
                isActive
                  ? 'bg-orange-500 text-white hover:bg-orange-600'
                  : 'bg-gray-200 text-gray-500 hover:bg-gray-300',
                (disabled || isPending) && 'opacity-50 cursor-not-allowed',
              )}
              disabled={disabled || isPending}
              aria-checked={isActive}
              role="switch"
            >
              <Zap className={cn('h-5 w-5', isActive ? 'text-white' : 'text-gray-500', isPending && 'animate-pulse')} />
            </button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Automation Status: {isActive ? 'Active' : 'Inactive'}</p>
            <p className="text-xs text-muted-foreground">
              {error
                ? error
                : isPending
                  ? 'Updating...'
                  : isActive
                    ? 'Click to deactivate this automation'
                    : 'Click to activate this automation'}
            </p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
