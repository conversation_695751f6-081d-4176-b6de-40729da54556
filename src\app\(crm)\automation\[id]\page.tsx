import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next';
import { Trash2 } from 'lucide-react';

import { Action, Field, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { AutomationToggle } from '@/components/ui/automation-toggle';
import { AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from '@/schemas/automations/types';
import { Button } from '@/components/ui/button';

import { edit, remove } from './actions';
import { getData } from './helpers';

export async function generateMetadata(
  { params }: { params: { id: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { automation } = await getData(params.id);
  const parentMetadata = await parent;

  return {
    title: `${automation.name} | ${parentMetadata.title?.absolute}`,
  };
}

export default async function AutomationDetailPage({ params }: { params: { id: string } }) {
  const { automation, canEdit, canDelete } = await getData(params.id);

  const editAction = edit.bind(null, params.id);
  const removeAction = remove.bind(null, params.id);

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{automation.name}</h1>
        {canDelete && (
          <Form action={removeAction}>
            <Action>
              <Button type="submit" variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </Action>
          </Form>
        )}
      </div>

      <script
        dangerouslySetInnerHTML={{
          __html: `
        // Wait for the page to fully load
        window.addEventListener('load', function() {
          console.log("Window loaded - initializing automation detail form");

          // Get the trigger and action select elements
          const triggerSelect = document.querySelector('select[name="triggerType"]');
          const actionSelect = document.querySelector('select[name="actionType"]');

          console.log("Trigger select:", triggerSelect);
          console.log("Action select:", actionSelect);

          // Function to show/hide trigger conditions based on selected trigger
          function updateTriggerConditions() {
            if (!triggerSelect) {
              console.log("Trigger select not found");
              return;
            }

            const selectedTrigger = triggerSelect.value;
            console.log("Selected trigger:", selectedTrigger);

            // Handle project status change trigger
            const projectStatusFields = document.querySelector('.trigger-conditions');

            if (projectStatusFields) {
              console.log("Found project status fields container");
              if (selectedTrigger === 'project_status_changed') {
                projectStatusFields.style.display = 'block';
                console.log("Showing project status fields");
              } else {
                projectStatusFields.style.display = 'none';
                console.log("Hiding project status fields");
              }
            } else {
              console.log("Project status fields container not found");
            }
          }

          // Function to show/hide action configurations based on selected action
          function updateActionConfigs() {
            if (!actionSelect) {
              console.log("Action select not found");
              return;
            }

            const selectedAction = actionSelect.value;
            console.log("Selected action:", selectedAction);

            // Hide all action configuration sections
            document.querySelectorAll('.action-config').forEach(section => {
              section.style.display = 'none';
              console.log("Hiding action config:", section);
            });

            // Show the appropriate action configuration section
            if (selectedAction === 'send_email') {
              const emailConfig = document.querySelector('.action-config[data-action-type="send_email"]');
              if (emailConfig) {
                console.log("Found email config container");
                emailConfig.style.display = 'block';
                console.log("Showing email config");
              } else {
                console.log("Email config container not found");
              }
            } else if (selectedAction === 'send_sms') {
              const smsConfig = document.querySelector('.action-config[data-action-type="send_sms"]');
              if (smsConfig) {
                console.log("Found SMS config container");
                smsConfig.style.display = 'block';
                console.log("Showing SMS config");
              } else {
                console.log("SMS config container not found");
              }
            }
          }

          // Add event listeners
          if (triggerSelect) {
            console.log("Adding change listener to trigger select");
            triggerSelect.addEventListener('change', updateTriggerConditions);

            // Initialize on page load
            console.log("Initializing trigger conditions");
            updateTriggerConditions();
          }

          if (actionSelect) {
            console.log("Adding change listener to action select");
            actionSelect.addEventListener('change', updateActionConfigs);

            // Initialize on page load
            console.log("Initializing action configs");
            updateActionConfigs();
          }

          // Force update after a short delay to ensure everything is loaded
          setTimeout(() => {
            console.log("Running delayed updates");
            updateTriggerConditions();
            updateActionConfigs();
          }, 500);
        });
      `,
        }}
      />

      <script
        dangerouslySetInnerHTML={{
          __html: `
        document.addEventListener('DOMContentLoaded', function() {
          // Find all user autocomplete fields
          const autocompleteFields = document.querySelectorAll('.user-autocomplete input');

          // Create a debounce function to limit API calls
          function debounce(func, wait) {
            let timeout;
            return function(...args) {
              clearTimeout(timeout);
              timeout = setTimeout(() => func.apply(this, args), wait);
            };
          }

          // Function to fetch user suggestions
          async function fetchUserSuggestions(query, type) {
            try {
              // This would be replaced with your actual API endpoint
              const response = await fetch(\`/api/users/suggestions?query=\${encodeURIComponent(query)}&type=\${type}\`);
              if (!response.ok) throw new Error('Failed to fetch suggestions');
              return await response.json();
            } catch (error) {
              console.error('Error fetching user suggestions:', error);
              return [];
            }
          }

          // Function to create and show suggestions dropdown
          function showSuggestions(input, suggestions, type) {
            // Remove any existing dropdown
            const existingDropdown = document.getElementById('autocomplete-dropdown');
            if (existingDropdown) existingDropdown.remove();

            // Create dropdown container
            const dropdown = document.createElement('div');
            dropdown.id = 'autocomplete-dropdown';
            dropdown.className = 'absolute z-50 bg-white border rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto';

            // Add suggestions to dropdown
            if (suggestions.length === 0) {
              const noResults = document.createElement('div');
              noResults.className = 'px-4 py-2 text-sm text-gray-500';
              noResults.textContent = 'No results found';
              dropdown.appendChild(noResults);
            } else {
              suggestions.forEach(suggestion => {
                const item = document.createElement('div');
                item.className = 'px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm';

                if (type === 'email') {
                  item.textContent = \`\${suggestion.name} <\${suggestion.email}>\`;
                } else {
                  item.textContent = \`\${suggestion.name} (\${suggestion.phone})\`;
                }

                item.addEventListener('click', () => {
                  // Get current value and add the new suggestion
                  const currentValue = input.value;
                  const values = currentValue.split(',').map(v => v.trim()).filter(v => v);

                  // Add the new value
                  const newValue = type === 'email' ? suggestion.email : suggestion.phone;
                  if (!values.includes(newValue)) {
                    values.push(newValue);
                  }

                  // Update input value
                  input.value = values.join(', ');

                  // Remove dropdown
                  dropdown.remove();
                });

                dropdown.appendChild(item);
              });
            }

            // Position and append dropdown
            const rect = input.getBoundingClientRect();
            dropdown.style.width = \`\${rect.width}px\`;

            // Append to body and position
            document.body.appendChild(dropdown);
            dropdown.style.position = 'absolute';
            dropdown.style.left = \`\${rect.left}px\`;
            dropdown.style.top = \`\${rect.bottom + window.scrollY}px\`;

            // Add event listener to close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
              if (!dropdown.contains(e.target) && e.target !== input) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
              }
            });
          }

          // Add event listeners to autocomplete fields
          autocompleteFields.forEach(input => {
            const field = input.closest('.user-autocomplete');
            const type = field.dataset.autocompleteType;

            // Debounced input handler
            const handleInput = debounce(async () => {
              const query = input.value.split(',').pop().trim();
              if (query.length < 2) return;

              const suggestions = await fetchUserSuggestions(query, type);
              showSuggestions(input, suggestions, type);
            }, 300);

            input.addEventListener('input', handleInput);
          });
        });
      `,
        }}
      />

      <Form action={editAction}>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-medium">Automation Details</h2>
          <AutomationToggle name="isActive" defaultChecked={automation.isActive} disabled={!canEdit} />
        </div>
        <Widget>
          <div className="space-y-4 p-4">
            <Field
              label="Name"
              name="name"
              placeholder="Enter a name for this automation"
              required
              defaultValue={automation.name}
              readOnly={!canEdit}
            />

            <Field
              label="Description"
              name="description"
              placeholder="Describe what this automation does"
              type="textarea"
              defaultValue={automation.description}
              readOnly={!canEdit}
            />

            <Field
              label="Trigger"
              name="triggerType"
              type="select"
              required
              defaultValue={automation.trigger?.type}
              options={Object.values(AUTOMATION_TRIGGER_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what triggers this automation"
              readOnly={!canEdit}
            />

            {automation.trigger.type === 'project_status_changed' && (
              <div className="trigger-conditions">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Field
                    label="From Status"
                    name="fromStatus"
                    type="select"
                    options={Object.values(PROJECT_STATUS).map((value) => ({
                      value,
                      label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                    }))}
                    placeholder="Select original status"
                    defaultValue={automation.trigger.projectStatusChangeConditions?.fromStatus}
                    readOnly={!canEdit}
                  />

                  <Field
                    label="To Status"
                    name="toStatus"
                    type="select"
                    options={Object.values(PROJECT_STATUS).map((value) => ({
                      value,
                      label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                    }))}
                    placeholder="Select new status"
                    defaultValue={automation.trigger.projectStatusChangeConditions?.toStatus}
                    readOnly={!canEdit}
                  />
                </div>

                <Field
                  label="Days of Inactivity"
                  name="daysOfInactivity"
                  type="number"
                  min="0"
                  help="Number of days with no activity before triggering"
                  placeholder="Enter number of days"
                  defaultValue={automation.trigger.projectStatusChangeConditions?.daysOfInactivity?.toString()}
                  readOnly={!canEdit}
                />
              </div>
            )}

            <Field
              label="Action"
              name="actionType"
              type="select"
              required
              options={Object.values(AUTOMATION_ACTION_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what action to perform"
              defaultValue={automation.actions[0]?.type}
              readOnly={!canEdit}
            />

            {automation.actions[0]?.type === 'send_email' && (
              <div className="action-config" data-action-type="send_email">
                <Field
                  label="Email Subject"
                  name="emailSubject"
                  placeholder="Enter email subject"
                  defaultValue={automation.actions[0]?.emailConfig?.subject}
                  readOnly={!canEdit}
                />

                <Field
                  label="Email Body"
                  name="emailBody"
                  type="textarea"
                  placeholder="Enter email content"
                  defaultValue={automation.actions[0]?.emailConfig?.body}
                  readOnly={!canEdit}
                />

                <Field
                  label="Recipients"
                  name="emailRecipientType"
                  type="select"
                  options={[
                    { value: 'user', label: 'Users in Organization' },
                    { value: 'custom', label: 'Custom Email Addresses' },
                  ]}
                  placeholder="Select recipient type"
                  defaultValue={automation.actions[0]?.emailConfig?.recipients?.[0]?.type}
                  readOnly={!canEdit}
                />

                {automation.actions[0]?.emailConfig?.recipients?.[0]?.type === 'custom' && (
                  <Field
                    label="Email Addresses"
                    name="customEmails"
                    placeholder="Enter email addresses, separated by commas"
                    help="Only used when 'Custom Email Addresses' is selected"
                    defaultValue={automation.actions[0]?.emailConfig?.recipients
                      ?.filter((r) => r.type === 'custom')
                      ?.map((r) => r.email)
                      ?.join(', ')}
                    readOnly={!canEdit}
                    className="user-autocomplete"
                    data-autocomplete-type="email"
                  />
                )}
              </div>
            )}

            {automation.actions[0]?.type === 'send_sms' && (
              <div className="action-config" data-action-type="send_sms">
                <Field
                  label="SMS Message"
                  name="smsMessage"
                  type="textarea"
                  placeholder="Enter SMS message"
                  defaultValue={automation.actions[0]?.smsConfig?.message}
                  readOnly={!canEdit}
                />

                <Field
                  label="Recipients"
                  name="smsRecipientType"
                  type="select"
                  options={[
                    { value: 'user', label: 'Users in Organization' },
                    { value: 'custom', label: 'Custom Phone Numbers' },
                  ]}
                  placeholder="Select recipient type"
                  defaultValue={automation.actions[0]?.smsConfig?.recipients?.[0]?.type}
                  readOnly={!canEdit}
                />

                {automation.actions[0]?.smsConfig?.recipients?.[0]?.type === 'custom' && (
                  <Field
                    label="Phone Numbers"
                    name="customPhones"
                    placeholder="Enter phone numbers, separated by commas"
                    help="Only used when 'Custom Phone Numbers' is selected"
                    defaultValue={automation.actions[0]?.smsConfig?.recipients
                      ?.filter((r) => r.type === 'custom')
                      ?.map((r) => r.phoneNumber)
                      ?.join(', ')}
                    readOnly={!canEdit}
                    className="user-autocomplete"
                    data-autocomplete-type="phone"
                  />
                )}
              </div>
            )}
          </div>
        </Widget>

        <Widget label="Automation History">
          <div className="p-4 space-y-2">
            <div className="text-sm">
              <span className="text-muted-foreground">Created by:</span>{' '}
              {automation.createdBy?.firstName && automation.createdBy?.lastName
                ? `${automation.createdBy.firstName} ${automation.createdBy.lastName}`
                : automation.createdBy?.email || 'Unknown'}{' '}
              on {new Date(automation.created).toLocaleDateString()}
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Last modified by:</span>{' '}
              {automation.modifiedBy?.firstName && automation.modifiedBy?.lastName
                ? `${automation.modifiedBy.firstName} ${automation.modifiedBy.lastName}`
                : automation.modifiedBy?.email || 'Unknown'}{' '}
              on {new Date(automation.modified).toLocaleDateString()}
            </div>
          </div>
        </Widget>

        <ActionFooter>
          <Link href="/automation" className="text-muted-foreground">
            Back to Automations
          </Link>
          {canEdit && <FormButton>Save Changes</FormButton>}
        </ActionFooter>
      </Form>
    </>
  );
}
