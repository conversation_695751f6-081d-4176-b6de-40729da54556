import mongoose from 'mongoose';

import { getSystemModel } from '@/schemas/system';

export async function migrationsV1() {
  const systemModel = await getSystemModel();
  let version = await systemModel.findOne({ key: 'dead_lead_reason_migration_v1' }).select('+value');
  if (!version) {
    version = new systemModel();
    version.key = 'dead_lead_reason_migration_v1';
    version.value = '0';
  }

  let dbVersion = Number(version.value);

  if (dbVersion < 1 && mongoose.connection.db) {
    const defaultDeadLeadReasons = [
      'Bad Lead from Lead Service',
      'Estimated Cost was Too High',
      'No Answer/No Response (Ghosted)',
      'No Damage',
      'Not Ready Yet',
      'Went with Someone Else',
      'Other',
      'Duplicate Lead',
      'Spam',
      'Not Enough Damage',
      'Marked Dead By Management',
      'Prospect Last Touched More Than 2 Months Ago',
      'Insurance Denied Claim',
      'Customer Canceled Claim',
      'No PM follow up/too long since last message',
      'Out of Service Area',
      'Lead Belongs In Other Location',
    ];

    const accounts = await mongoose.connection.db.collection('account').find().toArray();

    for (const account of accounts) {
      for (const defaultDeadLeadReason of defaultDeadLeadReasons) {
        await mongoose.connection.db
          .collection('dead-lead-reason')
          .insertOne({ name: defaultDeadLeadReason, account: account._id, created: new Date(), modified: new Date() });
      }
    }

    version.value = '1';
    dbVersion = 1;
    await version.save();
  }
}
